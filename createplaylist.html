<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/createplaylist.css">
    <title>Document</title>
</head>

<body>

     <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html" aria-current="page">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu"
                    aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container">
        <div class="create-playlist-page">
            <div class="page-header">
                <h1 class="playlist-title-gradient">Create Playlist</h1>
                <p class="page-description">Build your own playlist and add your favorite tracks</p>
            </div>
            <form class="create-playlist-form" autocomplete="off">
                <div class="form-group">
                    <label for="playlistName">Playlist Name</label>
                    <input type="text" id="playlistName" name="playlistName" maxlength="50" required placeholder="Enter playlist name">
                </div>
                <div class="form-group">
                    <label for="playlistDescription">Description <span class="optional">(optional)</span></label>
                    <textarea id="playlistDescription" name="playlistDescription" maxlength="200" placeholder="Describe your playlist"></textarea>
                </div>
                <!-- Optionally, add a song selection UI here in the future -->
                <button type="submit" class="action-btn create-btn">
                    <i class="fas fa-plus-circle"></i>
                    Create Playlist
                </button>
            </form>
            <div id="form-success" class="form-success-message">Playlist created successfully!</div>
        </div>
    </main>
    <script src="js/createplaylist.js"></script>
</body>
</html>