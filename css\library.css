:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}





/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Add grid for library cards */
.library-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 24px;
    margin-bottom: 2rem;
}

/* Card styles for library */
.card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 10px 24px rgba(var(--neon-blue-rgb),0.10), 0 2px 10px rgba(0,0,0,0.13);
    border-color: var(--neon-blue);
}

.card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.3em;
}

.card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Section title */
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Add margin to mini player */
.mini-player {
    margin-bottom: 0;
}

/* Hero section refinement */
.library-hero {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 4rem 2rem 3rem 2rem;
    margin-top: 120px; /* Increased to push hero below navbar */
    margin-bottom: 3rem;
    border-radius: 18px;
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 320px;
    overflow: hidden;
}

.library-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 60% 40%, rgba(111,0,255,0.12) 0%, transparent 70%),
                linear-gradient(120deg, rgba(0,224,255,0.08) 0%, rgba(255,0,110,0.10) 100%);
    opacity: 0.7;
    z-index: 1;
    pointer-events: none;
    border-radius: inherit;
}

.particles-container {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, var(--neon-blue), var(--cosmic-pink), transparent 70%);
    border-radius: 50%;
    opacity: 0.13; /* was 0.28, now more transparent */
    box-shadow: 
        0 0 16px 6px rgba(111,0,255,0.09),   /* was 0.18 */
        0 0 32px 12px rgba(0,224,255,0.05),  /* was 0.10 */
        0 0 24px 8px rgba(255,0,110,0.04);   /* was 0.08 */
    animation-name: particleDrift;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-duration: inherit; /* Use JS-assigned duration */
}

@keyframes particleDrift {
    0% {
        opacity: 0;
        transform: translate(0, 0) scale(1);
    }
    10% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
        transform: translate(var(--dx, 40px), var(--dy, 40px)) scale(1.3);
    }
    90% {
        opacity: 0.7;
    }
    100% {
        opacity: 0;
        transform: translate(0, 0) scale(1);
    }
}

.library-hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    z-index: 3;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
    position: relative;
    z-index: 3;
}

.library-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 3;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.04);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    /* Add gradient color to icon */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.28);
    /* Increased text-shadow opacity for more glow */
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #fff; /* Ghost white/white */
    display: block;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Library Search Section Styles */
.library-search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.library-search-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background: rgba(255,255,255,0.05);
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.10);
    padding: 0.5rem 0.5rem 0.5rem 1.2rem;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.2s, border 0.2s;
}

.library-search-bar:focus-within {
    border: 1.5px solid var(--cosmic-pink);
    box-shadow: 0 0 0 2px rgba(255,0,110,0.18), 0 0 12px 2px rgba(0,224,255,0.12);
}

.library-search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 0.6rem 2.2rem 0.6rem 0.2rem;
    border-radius: 30px;
}

.library-search-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.library-search-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.library-search-btn:hover,
.library-search-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.08);
}

.library-search-clear {
    position: absolute;
    right: 55px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    z-index: 2;
}

.library-search-clear:hover {
    color: var(--cosmic-pink);
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 0.7rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    font-size: 1rem;
}

.filter-btn:hover,
.filter-btn:focus {
    background: rgba(255, 255, 255, 0.12);
    color: var(--neon-blue);
    outline: none;
}

.filter-btn.active {
    background: var(--button-gradient);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
}

/* Results Count & Container */
.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
    text-align: left;
    width: 100%;
    max-width: 500px;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 80px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.2rem;
    color: var(--text-primary);
    font-size: 1.08rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.08rem;
    text-align: center;
    opacity: 0.7;
}

/* Unify card grid layout for all main card containers */
.library-cards,
.liked-songs-cards,
.recently-played-cards,
.top-artists-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
}

/* Card styles for library */
.card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 10px 24px rgba(var(--neon-blue-rgb),0.10), 0 2px 10px rgba(0,0,0,0.13);
    border-color: var(--neon-blue);
}

.card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.3em;
}

.card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Section title */
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Add margin to mini player */
.mini-player {
    margin-bottom: 0;
}

/* Hero section refinement */
.library-hero {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 4rem 2rem 3rem 2rem;
    margin-top: 120px; /* Increased to push hero below navbar */
    margin-bottom: 3rem;
    border-radius: 18px;
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 320px;
    overflow: hidden;
}

.library-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 60% 40%, rgba(111,0,255,0.12) 0%, transparent 70%),
                linear-gradient(120deg, rgba(0,224,255,0.08) 0%, rgba(255,0,110,0.10) 100%);
    opacity: 0.7;
    z-index: 1;
    pointer-events: none;
    border-radius: inherit;
}

.particles-container {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, var(--neon-blue), var(--cosmic-pink), transparent 70%);
    border-radius: 50%;
    opacity: 0.13; /* was 0.28, now more transparent */
    box-shadow: 
        0 0 16px 6px rgba(111,0,255,0.09),   /* was 0.18 */
        0 0 32px 12px rgba(0,224,255,0.05),  /* was 0.10 */
        0 0 24px 8px rgba(255,0,110,0.04);   /* was 0.08 */
    animation-name: particleDrift;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-duration: inherit; /* Use JS-assigned duration */
}

@keyframes particleDrift {
    0% {
        opacity: 0;
        transform: translate(0, 0) scale(1);
    }
    10% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
        transform: translate(var(--dx, 40px), var(--dy, 40px)) scale(1.3);
    }
    90% {
        opacity: 0.7;
    }
    100% {
        opacity: 0;
        transform: translate(0, 0) scale(1);
    }
}

.library-hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    z-index: 3;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
    position: relative;
    z-index: 3;
}

.library-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 3;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.04);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    /* Add gradient color to icon */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.28);
    /* Increased text-shadow opacity for more glow */
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #fff; /* Ghost white/white */
    display: block;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Library Search Section Styles */
.library-search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.library-search-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background: rgba(255,255,255,0.05);
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.10);
    padding: 0.5rem 0.5rem 0.5rem 1.2rem;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.2s, border 0.2s;
}

.library-search-bar:focus-within {
    border: 1.5px solid var(--cosmic-pink);
    box-shadow: 0 0 0 2px rgba(255,0,110,0.18), 0 0 12px 2px rgba(0,224,255,0.12);
}

.library-search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 0.6rem 2.2rem 0.6rem 0.2rem;
    border-radius: 30px;
}

.library-search-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.library-search-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.library-search-btn:hover,
.library-search-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.08);
}

.library-search-clear {
    position: absolute;
    right: 55px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    z-index: 2;
}

.library-search-clear:hover {
    color: var(--cosmic-pink);
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 0.7rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    font-size: 1rem;
}

.filter-btn:hover,
.filter-btn:focus {
    background: rgba(255, 255, 255, 0.12);
    color: var(--neon-blue);
    outline: none;
}

.filter-btn.active {
    background: var(--button-gradient);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
}

/* Results Count & Container */
.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
    text-align: left;
    width: 100%;
    max-width: 500px;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 80px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.2rem;
    color: var(--text-primary);
    font-size: 1.08rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.08rem;
    text-align: center;
    opacity: 0.7;
}

/* --- Option 6: Improved mobile tweaks --- */
@media (max-width: 600px) {
    .library-section,
    .library-search-section,
    .library-hero {
        padding-left: 2vw;
        padding-right: 2vw;
    }
    .section-header .section-title {
        font-size: 1.1em;
    }
    .create-playlist-btn,
    .play-all-btn {
        width: 100%;
        justify-content: center;
        font-size: 1em;
        padding: 0.7em 0;
        min-width: 0;
    }
    .library-cards,
    .liked-songs-cards,
    .recently-played-cards,
    .top-artists-cards {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 100%;
    }
    .card {
        max-width: 100%;
        min-width: 0;
    }
}

/* --- Option 7: Minor cleanup --- */
/* Remove duplicate/old .library-cards.centered and similar rules if present */
/* ...existing code... */

/* Consistent section container for library sections */
.library-section {
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.18);
    padding: 2.5rem 2rem 2rem 2rem;
    margin: 2.5rem auto 2.5rem auto;
    max-width: 1100px;
    width: 100%;
    position: relative;
    z-index: 1;
    /* Add subtle border for depth */
    border: 1.5px solid rgba(255,255,255,0.10);
    transition: box-shadow 0.3s, background 0.3s;
}

/* Section header and divider consistency */
.section-header {
    text-align: center;
    margin-bottom: 1.2rem;
    position: relative;
}
.section-header .section-title {
    font-size: 2em;
    font-weight: 800;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    margin-bottom: 0.3em;
}
.section-divider {
    width: 100%;
    max-width: 700px;
    height: 2px;
    margin: 0.5em auto 1.2em auto;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    opacity: 0.12;
    border-radius: 2px;
}

/* Section description consistency */
.section-description {
    color: var(--text-secondary);
    font-size: 1.05em;
    margin-bottom: 1.2rem;
    margin-top: 0;
    text-align: center;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive adjustments for section containers */
@media (max-width: 1200px) {
    .library-section {
        max-width: 98vw;
        padding: 1.5rem 0.5rem 1rem 0.5rem;
    }
    .section-header .section-title {
        font-size: 1.3em;
    }
}

/* Widen main containers for full-width look */
.library-section,
.library-search-section,
.library-hero {
    max-width: none;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    border-radius: 0;
    padding-left: 3vw;
    padding-right: 3vw;
}

/* Remove max-width from cards grids for full stretch */
.library-cards,
.liked-songs-cards,
.recently-played-cards,
.top-artists-cards {
    max-width: none;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}

/* Consistent card hover/focus effect */
.card,
.playlist-card,
.liked-song-card,
.recently-played-card,
.top-artist-card {
    transition: box-shadow 0.25s, transform 0.25s, background 0.25s;
}
.card:focus-within,
.card:hover,
.playlist-card:focus-within,
.playlist-card:hover,
.liked-song-card:focus-within,
.liked-song-card:hover,
.recently-played-card:focus-within,
.recently-played-card:hover,
.top-artist-card:focus-within,
.top-artist-card:hover {
    box-shadow: 0 8px 24px rgba(0,224,255,0.13), 0 2px 10px rgba(255,0,110,0.10);
    background: linear-gradient(120deg, rgba(0,224,255,0.13) 0%, rgba(255,0,110,0.13) 100%);
    transform: translateY(-4px) scale(1.02);
    z-index: 2;
    outline: none;
}

/* Responsive: keep some padding on small screens */
@media (max-width: 900px) {
    .library-section,
    .library-search-section,
    .library-hero {
        padding-left: 1vw;
        padding-right: 1vw;
    }
}

/* Playlist Card Enhancements */
.playlist-card {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    outline: none;
    transition: box-shadow 0.2s, transform 0.2s;
    width: 100%;
    max-width: 320px; /* Match liked-song-card max width */
    min-width: 220px;
}
.playlist-card:focus {
    box-shadow: 0 0 0 3px var(--neon-blue), 0 2px 12px rgba(0,224,255,0.18);
    z-index: 2;
}
.playlist-card .img-container {
    width: 100%;
    height: 160px;
    position: relative;
    overflow: hidden;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.13);
}
.playlist-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: brightness(0.93);
}
.playlist-card:hover img,
.playlist-card:focus img {
    transform: scale(1.07);
    filter: brightness(1.08) contrast(1.08);
}

/* Play overlay on hover/focus */
.play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}
.playlist-card:hover .play-overlay,
.playlist-card:focus .play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.play-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,224,255,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.play-button:hover,
.play-button:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

/* Playlist meta info */
.playlist-meta {
    display: flex;
    gap: 0.7em;
    justify-content: center;
    align-items: center;
    margin-top: 0.7em;
    font-size: 0.98em;
}
.playlist-count {
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.3em;
    font-size: 0.97em;
}
.playlist-count i {
    color: var(--neon-blue);
    font-size: 1em;
}
.playlist-tag {
    background: rgba(0,224,255,0.09);
    color: var(--neon-blue);
    border-radius: 12px;
    padding: 0.15em 0.7em;
    font-size: 0.93em;
    font-weight: 600;
    letter-spacing: 0.01em;
}

/* Responsive tweaks */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.7em;
    }
    .section-description {
        font-size: 0.98em;
    }
    .playlist-meta {
        flex-direction: column;
        gap: 0.3em;
    }
}
@media (max-width: 480px) {
    .create-playlist-btn {
        font-size: 0.95em;
        padding: 0.5em 1em;
    }
    .playlist-meta {
        font-size: 0.93em;
    }
}

/* Liked Songs Card Content Consistency */
.liked-song-card .card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.7em;
}

.liked-song-card .text-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.3em;
}

.liked-song-card .card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.2em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
}

.liked-song-card .card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0;
    max-width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Play Now button for liked songs */
.play-now-btn {
    margin-top: auto;
    display: inline-block;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    padding: 8px 16px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.play-now-btn:hover,
.play-now-btn:focus {
    transform: translateY(-2px);
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    text-shadow:
        0 0 10px rgba(var(--neon-blue-rgb), 0.8),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.4);
    outline: 2px solid var(--cosmic-pink);
    outline-offset: 2px;
}

/* Recently Played Cards Grid */
.recently-played-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
    gap: 24px;
    justify-items: center;
}

/* Recently Played Card (inherits from .card, .liked-song-card, .playlist-card) */
.recently-played-card {
    width: 100%;
    max-width: 320px;
    min-width: 220px;
    /* Use same style as .playlist-card/.liked-song-card for consistency */
}

/* Responsive: match other card breakpoints */
@media (max-width: 768px) {
    .recently-played-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .recently-played-card {
        max-width: 200px;
        min-width: 120px;
    }
    .recently-played-card .img-container {
        height: 100px;
    }
    .recently-played-card .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .recently-played-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .recently-played-card {
        max-width: 100%;
        min-width: 0;
    }
    .recently-played-card .img-container {
        height: 80px;
    }
    .recently-played-card .card-content {
        padding: 0.5rem;
    }
}

/* Top Artists Cards Grid */
.top-artists-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
    gap: 24px;
    justify-items: center;
}

/* Top Artist Card (inherits from .card) */
.top-artist-card {
    width: 100%;
    max-width: 320px;
    min-width: 220px;
    /* Use same style as .playlist-card/.liked-song-card for consistency */
}

/* Responsive: match other card breakpoints */
@media (max-width: 768px) {
    .top-artists-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .top-artist-card {
        max-width: 200px;
        min-width: 120px;
    }
    .top-artist-card .img-container {
        height: 100px;
    }
    .top-artist-card .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .top-artists-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .top-artist-card {
        max-width: 100%;
        min-width: 0;
    }
    .top-artist-card .img-container {
        height: 80px;
    }
    .top-artist-card .card-content {
        padding: 0.5rem;
    }
}

.filter-select:hover {
    background: rgba(255, 255, 255, 0.1);
}

.filter-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--neon-blue);
}

/* Responsive improvements for hero */
@media (max-width: 768px) {
    .library-hero {
        padding: 2.5rem 0.5rem 1.5rem 0.5rem;
        margin-top: 100px; /* Still enough space below navbar on tablet */
    }
    .library-hero-title {
        font-size: 2rem;
    }
    .hero-subtitle {
        font-size: 1.1rem;
    }
    .library-stats {
        flex-direction: column;
        gap: 1.2rem;
    }
    .stat-item {
        padding: 1rem 1.2rem;
    }
}

@media (max-width: 480px) {
    .library-hero {
        padding: 1.5rem 0.2rem 1rem 0.2rem;
        margin-top: 80px; /* For mobile, still clear of navbar */
    }
    .library-hero-title {
        font-size: 1.3rem;
    }
    .stat-item {
        padding: 0.8rem 0.5rem;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .library-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .card img {
        height: 100px;
    }
    .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .library-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .card img {
        height: 80px;
    }
    .card-content {
        padding: 0.5rem;
    }
}

/* Centering styles for Your Playlists section */
.centered {
    text-align: center !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure .section-header.centered uses flex and centers children */
.section-header.centered {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.7em;
    text-align: center;
}

/* Center the Create Playlist button and Play All button */
.create-playlist-btn,
.play-all-btn {
    margin-left: 0;
    margin-right: 0;
}

/* Center the cards grid for this section and ensure grid layout */
.library-cards.centered {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive: stack cards on small screens */
@media (max-width: 768px) {
    .library-cards.centered {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 14px;
        max-width: 100%;
    }
}
@media (max-width: 480px) {
    .library-cards.centered {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 100%;
    }
}
.playlist-sort {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-top: 0.5em;
    justify-content: center;
    margin-bottom: 1.2em;
}

.playlist-sort select {
    background: rgba(19,21,26,0.95);
    color: var(--text-primary);
    border: 1.5px solid rgba(255,255,255,0.18);
    border-radius: 18px;
    padding: 0.5em 2.2em 0.5em 1em;
    font-size: 1em;
    font-weight: 600;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    transition: border 0.2s, background 0.2s, box-shadow 0.2s;
    appearance: none;
    cursor: pointer;
    min-width: 150px;
    position: relative;
}

.playlist-sort select:focus {
    border: 1.5px solid var(--neon-blue);
    background: rgba(26,29,36,0.98);
    box-shadow: 0 0 0 2px var(--neon-blue);
}

.playlist-sort select:hover {
    background: rgba(19,21,26,1);
    border-color: var(--cosmic-pink);
}

.playlist-sort::after {
    content: "\f078";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: var(--neon-blue);
    position: absolute;
    right: 1.2em;
    pointer-events: none;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.95em;
    z-index: 2;
    display: none; /* fallback for browsers not supporting ::after on flex */
}

/* Responsive: make dropdown full width on mobile */
@media (max-width: 600px) {
    .playlist-sort select {
        width: 100%;
        min-width: 0;
        font-size: 1em;
        padding: 0.6em 1em;
    }
}
    cursor: pointer;
}
.playlist-card:hover .playlist-title-hover,
.playlist-card:focus .playlist-title-hover {
    color: var(--neon-blue);
    text-shadow: 0 0 8px rgba(0,224,255,0.18), 0 0 16px rgba(255,0,110,0.10);
}

/* Playlist icon above title */
.playlist-icon {
    font-size: 2.1rem;
    color: var(--neon-blue);
    margin-bottom: 0.25em;
    margin-top: 0.2em;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.18);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Play All button for liked songs */
.play-all-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 0.6em 1.3em;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    margin-left: 1em;
}
.play-all-btn i {
    font-size: 1.1em;
}
.play-all-btn:hover,
.play-all-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Liked Songs Cards Grid - match playlist card width */
.liked-songs-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
    gap: 24px;
    justify-items: center;
}

/* Make liked song cards the same width as playlist cards */
.liked-song-card {
    width: 100%;
    max-width: 320px; /* Match playlist-card max width */
    min-width: 220px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    outline: none;
    transition: box-shadow 0.2s, transform 0.2s;
}
.liked-song-card:focus {
    box-shadow: 0 0 0 3px var(--cosmic-pink), 0 2px 12px rgba(255,0,110,0.18);
    z-index: 2;
}
.liked-song-card .img-container {
    width: 100%;
    height: 160px;
    position: relative;
    overflow: hidden;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.13);
}
.liked-song-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: brightness(0.93);
}
.liked-song-card:hover img,
.liked-song-card:focus img {
    transform: scale(1.07);
    filter: brightness(1.08) contrast(1.08);
}

/* Play overlay on hover/focus */
.liked-song-card .play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}
.liked-song-card:hover .play-overlay,
.liked-song-card:focus .play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.liked-song-card .play-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(255,0,110,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.liked-song-card .play-button:hover,
.liked-song-card .play-button:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

/* Song number in card */
.song-number {
    position: absolute;
    top: 10px;
    left: 12px;
    background: rgba(0,0,0,0.55);
    color: #fff;
    font-size: 1.1em;
    font-weight: 700;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    border: 1.5px solid var(--cosmic-pink);
}

/* Liked heart icon */
.liked-icon {
    position: absolute;
    top: 10px;
    right: 44px;
    color: var(--cosmic-pink);
    font-size: 1.2em;
    z-index: 3;
    background: rgba(255,255,255,0.08);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255,0,110,0.08);
}

/* Song card menu button (three dots) */
.song-menu-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(19,21,26,0.7);
    border: none;
    color: var(--text-secondary);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    opacity: 0.85;
}
.song-menu-btn:hover,
.song-menu-btn:focus {
    background: var(--gradient-primary);
    color: #fff;
    outline: none;
    box-shadow: 0 0 0 2px var(--cosmic-pink);
}

/* Song card title hover/focus effect */
.liked-song-title-hover {
    transition: color 0.2s, text-shadow 0.2s;
    cursor: pointer;
}
.liked-song-card:hover .liked-song-title-hover,
.liked-song-card:focus .liked-song-title-hover {
    color: var(--cosmic-pink);
    text-shadow: 0 0 8px rgba(255,0,110,0.18), 0 0 16px rgba(0,224,255,0.10);
}

/* Create Playlist Button Styling */
.create-playlist-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 0.7em 1.6em;
    font-size: 1.08em;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.6em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    margin-left: 1em;
    margin-top: 0.2em;
    margin-bottom: 0.2em;
}
.create-playlist-btn i {
    font-size: 1.1em;
}
.create-playlist-btn:hover,
.create-playlist-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Responsive: shrink button on small screens */
@media (max-width: 480px) {
    .create-playlist-btn {
        font-size: 0.95em;
        padding: 0.5em 1em;
    }
}

/* Responsive: stack cards on smaller screens */
@media (max-width: 900px) {
    .library-cards,
    .liked-songs-cards,
    .recently-played-cards,
    .top-artists-cards {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 14px;
        max-width: 100%;
    }
}
@media (max-width: 600px) {
    .library-cards,
    .liked-songs-cards,
    .recently-played-cards,
    .top-artists-cards {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 100%;
    }
}

/* Enhanced Library Styles */

/* Enhanced Hero Section */
.library-hero {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, rgba(0, 224, 255, 0.1), rgba(255, 0, 110, 0.1), rgba(111, 0, 255, 0.1));
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.library-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 224, 255, 0.2) 0%,
        rgba(255, 0, 110, 0.2) 50%,
        rgba(111, 0, 255, 0.2) 100%
    );
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 4rem 2rem;
}

/* Enhanced Statistics */
.library-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    transition: transform var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
    transition: all var(--transition-normal);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Enhanced Cards */
.library-card,
.playlist-card,
.liked-song-card,
.artist-card {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.library-card::before,
.playlist-card::before,
.liked-song-card::before,
.artist-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.library-card:hover::before,
.playlist-card:hover::before,
.liked-song-card:hover::before,
.artist-card:hover::before {
    left: 100%;
}

.library-card:hover,
.playlist-card:hover,
.liked-song-card:hover,
.artist-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: var(--neon-blue);
}

/* Enhanced Buttons */
.create-playlist-btn,
.play-all-btn,
.action-btn {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.create-playlist-btn::before,
.play-all-btn::before,
.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.create-playlist-btn:hover::before,
.play-all-btn:hover::before,
.action-btn:hover::before {
    left: 100%;
}

.create-playlist-btn:hover,
.play-all-btn:hover,
.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

/* Enhanced Section Headers */
.section-header h2 {
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    transition: width var(--transition-normal);
    border-radius: 2px;
}

.section-header:hover h2::after {
    width: 100%;
}

/* Enhanced Particles Animation */
.particles-container .particle {
    animation-duration: 15s, 20s, 25s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

.particles-container .particle:nth-child(odd) {
    background: radial-gradient(circle, var(--neon-blue), transparent);
}

.particles-container .particle:nth-child(even) {
    background: radial-gradient(circle, var(--cosmic-pink), transparent);
}

.particles-container .particle:nth-child(3n) {
    background: radial-gradient(circle, var(--electric-violet), transparent);
}

/* Enhanced Search Functionality */
.library-search-input {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 0.75rem 3rem 0.75rem 3rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-normal);
    width: 100%;
    max-width: 400px;
}

.library-search-input:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 4px rgba(0, 224, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
    font-size: 1rem;
    z-index: 2;
}

.search-clear {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-fast);
    opacity: 0;
    visibility: hidden;
}

.library-search-input:not(:placeholder-shown) + .search-clear {
    opacity: 1;
    visibility: visible;
}

.search-clear:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Enhanced Loading States */
.loading-shimmer {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Accessibility */
.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Enhanced Focus States */
.library-card:focus,
.playlist-card:focus,
.liked-song-card:focus,
.artist-card:focus,
.create-playlist-btn:focus,
.play-all-btn:focus,
.action-btn:focus,
.library-search-input:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .library-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-content {
        padding: 3rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .library-stats {
        gap: 1.5rem;
        flex-direction: column;
        align-items: center;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        width: 100%;
        max-width: 200px;
        text-align: left;
    }

    .stat-number {
        font-size: 1.5rem;
        margin-bottom: 0;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .search-input-wrapper {
        max-width: 100%;
    }
}
