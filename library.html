<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/library.css">
    <title>Your Library - BansheeBlast</title>
</head>
<body>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html" aria-current="page">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="library-container">
        <!-- Hero Section with Dynamic Stats -->
        <section class="library-hero glimmer-background" aria-label="Your Music Universe">
            <div class="hero-content">
                <div class="particles-container" aria-hidden="true">
                    <!-- 15 animated particles for effect -->
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                </div>
                <h1 class="library-hero-title">Your Music Universe</h1>
                <p class="hero-subtitle">Discover your personal collection and favorites</p>
                <div class="library-stats" role="list" aria-label="Library statistics">
                    <div class="stat-item" role="listitem">
                        <span class="stat-icon"><i class="fas fa-list"></i></span>
                        <span class="stat-number">12</span>
                        <span class="stat-label">Playlists</span>
                    </div>
                    <div class="stat-item" role="listitem">
                        <span class="stat-icon"><i class="fas fa-heart"></i></span>
                        <span class="stat-number">87</span>
                        <span class="stat-label">Liked Songs</span>
                    </div>
                    <div class="stat-item" role="listitem">
                        <span class="stat-icon"><i class="fas fa-user-friends"></i></span>
                        <span class="stat-number">34</span>
                        <span class="stat-label">Following</span>
                    </div>
                </div>
            </div>
        </section>
        <!-- End of Hero Section -->

        <!-- Enhanced Search Bar Section -->
        <section class="library-search-section" aria-label="Search your library">
            <form class="library-search-bar" role="search" aria-label="Library Search">
                <input type="search" class="library-search-input" placeholder="Search for songs, artists, or playlists" aria-label="Search your library" minlength="2" required>
                <button type="button" class="library-search-clear" aria-label="Clear search">
                    <i class="fas fa-times"></i>
                </button>
                <button type="submit" class="library-search-btn" aria-label="Search">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <div class="search-filters" aria-label="Filter search results">
                <button type="button" class="filter-btn active" data-filter="all">All</button>
                <button type="button" class="filter-btn" data-filter="playlists">Playlists</button>
                <button type="button" class="filter-btn" data-filter="songs">Songs</button>
                <button type="button" class="filter-btn" data-filter="artists">Artists</button>
            </div>
            <div class="results-count" aria-live="polite"></div>
            <div class="results-container" id="resultsContainer">
                <span class="empty-results">Start typing to search your library.</span>
            </div>
        </section>
        <!-- End of Search Bar Section -->

        <!-- Playlists Section -->
        <section class="library-section centered" aria-labelledby="your-playlists-title">
            <div class="section-header centered">
                <h2 class="section-title" id="your-playlists-title">Your Playlists</h2>
                <button class="create-playlist-btn" aria-label="Create new playlist">
                    <i class="fas fa-plus"></i> Create Playlist
                </button>
            </div>
            <div class="section-divider" aria-hidden="true"></div>
            <div class="section-toolbar">
                <p class="section-description centered">
                    All your curated playlists in one place. Click a playlist to play or manage it.
                </p>
                <div class="playlist-sort" style="position:relative;">
                    <label for="playlistSort" class="visually-hidden">Sort playlists</label>
                    <select id="playlistSort" aria-label="Sort playlists">
                        <option value="recent">Recently Added</option>
                        <option value="alpha">A-Z</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
            </div>
            <div class="library-cards centered">
                <!-- Playlist Card 1 -->
                <div class="card playlist-card" tabindex="0" aria-label="Chill Beats playlist, 32 tracks, Genre: Lo-fi">
                    <div class="img-container">
                        <img src="imgs/playlist-01.png" alt="Chill Beats Playlist Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Chill Beats">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Chill Beats</h3>
                            <p>32 tracks • Lo-fi, Chillhop</p>
                        </div>
                        <a href="#" class="button">Open Playlist</a>
                    </div>
                </div>
                <!-- Playlist Card 2 -->
                <div class="card playlist-card" tabindex="0" aria-label="Workout Hits playlist, 24 tracks, Genre: Pop">
                    <div class="img-container">
                        <img src="imgs/playlist-02.png" alt="Workout Hits Playlist Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Workout Hits">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Workout Hits</h3>
                            <p>24 tracks • Pop</p>
                        </div>
                        <a href="#" class="button">Open Playlist</a>
                    </div>
                </div>
                <!-- Playlist Card 3 -->
                <div class="card playlist-card" tabindex="0" aria-label="Indie Gems playlist, 18 tracks, Genre: Indie">
                    <div class="img-container">
                        <img src="imgs/playlist-03.png" alt="Indie Gems Playlist Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Indie Gems">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Indie Gems</h3>
                            <p>18 tracks • Indie</p>
                        </div>
                        <a href="#" class="button">Open Playlist</a>
                    </div>
                </div>
                <!-- Playlist Card 4 -->
                <div class="card playlist-card" tabindex="0" aria-label="Party Time playlist, 40 tracks, Genre: Dance">
                    <div class="img-container">
                        <img src="imgs/playlist-04.png" alt="Party Time Playlist Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Party Time">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Party Time</h3>
                            <p>40 tracks • Dance</p>
                        </div>
                        <a href="#" class="button">Open Playlist</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Liked Songs Section -->
        <section class="library-section">
            <div class="section-header centered">
                <h2 class="section-title">Your Liked Songs</h2>
                <button class="play-all-btn" aria-label="Play all liked songs">
                    <i class="fas fa-play"></i> Play All
                </button>
            </div>
            <div class="section-divider" aria-hidden="true"></div>
            <div class="section-toolbar">
                <p class="section-description centered">
                    All your favorite tracks in one place. Click to play instantly.
                </p>
            </div>
            <div class="library-cards liked-songs-cards centered">
                <!-- Liked Song Card 1 -->
                <div class="card liked-song-card" tabindex="0" aria-label="Song 1 by Artist 1, liked">
                    <div class="img-container">
                        <img src="imgs/album-01.png" alt="Song 1 Album Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Song 1">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Song 1</h3>
                            <p>Artist 1 • Album 1</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
                <!-- Liked Song Card 2 -->
                <div class="card liked-song-card" tabindex="0" aria-label="Song 2 by Artist 2, liked">
                    <div class="img-container">
                        <img src="imgs/album-02.png" alt="Song 2 Album Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Song 2">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Song 2</h3>
                            <p>Artist 2 • Album 2</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
                <!-- Liked Song Card 3 -->
                <div class="card liked-song-card" tabindex="0" aria-label="Song 3 by Artist 3, liked">
                    <div class="img-container">
                        <img src="imgs/album-03.png" alt="Song 3 Album Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Song 3">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Song 3</h3>
                            <p>Artist 3 • Album 3</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recently Played Section -->
        <section class="library-section">
            <div class="section-header centered">
                <h2 class="section-title">Recently Played</h2>
            </div>
            <div class="section-divider" aria-hidden="true"></div>
            <div class="section-toolbar">
                <p class="section-description centered">
                    Your most recently played tracks and playlists.
                </p>
            </div>
            <div class="library-cards recently-played-cards centered">
                <!-- Recently Played Card 1 -->
                <div class="card recently-played-card" tabindex="0" aria-label="Retro Dreams by Artist 1, Album">
                    <div class="img-container">
                        <img src="imgs/album-04.png" alt="Retro Dreams Album Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Retro Dreams">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Retro Dreams</h3>
                            <p>Artist 1 • Album</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
                <!-- Recently Played Card 2 -->
                <div class="card recently-played-card" tabindex="0" aria-label="Workout Hits Playlist">
                    <div class="img-container">
                        <img src="imgs/playlist-02.png" alt="Workout Hits Playlist Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Workout Hits">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Workout Hits</h3>
                            <p>Playlist</p>
                        </div>
                        <a href="#" class="button">Open Playlist</a>
                    </div>
                </div>
                <!-- Recently Played Card 3 -->
                <div class="card recently-played-card" tabindex="0" aria-label="Song 2 by Artist 2, Song">
                    <div class="img-container">
                        <img src="imgs/album-02.png" alt="Song 2 Album Cover" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Song 2">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Song 2</h3>
                            <p>Artist 2 • Song</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Top Artists Section -->
        <section class="library-section">
            <div class="section-header centered">
                <h2 class="section-title">Top Artists</h2>
            </div>
            <div class="section-divider" aria-hidden="true"></div>
            <div class="section-toolbar">
                <p class="section-description centered">
                    Your most played and followed artists.
                </p>
            </div>
            <div class="library-cards top-artists-cards centered">
                <!-- Top Artist Card 1 -->
                <div class="card top-artist-card" tabindex="0" aria-label="Artist Alpha, Indie Pop">
                    <div class="img-container">
                        <img src="imgs/album-01.png" alt="Artist Alpha" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Artist Alpha">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Artist Alpha</h3>
                            <p>Indie Pop</p>
                        </div>
                        <a href="#" class="button">View Artist</a>
                    </div>
                </div>
                <!-- Top Artist Card 2 -->
                <div class="card top-artist-card" tabindex="0" aria-label="Beta Beats, Lo-fi Hip Hop">
                    <div class="img-container">
                        <img src="imgs/album-02.png" alt="Beta Beats" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Beta Beats">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Beta Beats</h3>
                            <p>Lo-fi Hip Hop</p>
                        </div>
                        <a href="#" class="button">View Artist</a>
                    </div>
                </div>
                <!-- Top Artist Card 3 -->
                <div class="card top-artist-card" tabindex="0" aria-label="Gamma Grooves, Funk/Soul">
                    <div class="img-container">
                        <img src="imgs/album-03.png" alt="Gamma Grooves" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play Gamma Grooves">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>Gamma Grooves</h3>
                            <p>Funk/Soul</p>
                        </div>
                        <a href="#" class="button">View Artist</a>
                    </div>
                </div>
            </div>
        </section>
        <!-- Add more sections as needed -->
    </main>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>
            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>
            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>
            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <footer>
        <div class="footer-content">
            <p>&copy; 2024 BansheeBlast. All rights reserved.</p>
        </div>
    </footer>
    <script src="js/main.js"></script>
    <script src="js/library.js"></script>
</body>
</html>