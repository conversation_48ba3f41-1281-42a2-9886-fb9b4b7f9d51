// Trending Charts Management System
class TrendingChartsManager {
    constructor() {
        this.currentChart = 'global';
        this.currentTimeRange = 'weekly';
        this.currentGenre = 'all';
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.loadInitialData();
    }

    bindElements() {
        // Chart navigation
        this.chartTabs = document.querySelectorAll('.chart-tab');
        this.timeRangeFilter = document.getElementById('timeRangeFilter');
        this.genreFilter = document.getElementById('genreFilter');

        // Featured hero
        this.featuredChartHero = document.getElementById('featuredChartHero');
        this.heroBgImage = document.getElementById('heroBgImage');
        this.heroTitle = document.getElementById('heroTitle');
        this.heroArtist = document.getElementById('heroArtist');
        this.heroPlays = document.getElementById('heroPlays');
        this.heroLikes = document.getElementById('heroLikes');
        this.heroShares = document.getElementById('heroShares');
        this.heroPlayBtn = document.getElementById('heroPlayBtn');
        this.heroLikeBtn = document.getElementById('heroLikeBtn');
        this.heroAddBtn = document.getElementById('heroAddBtn');

        // Chart sections
        this.songsChartList = document.getElementById('songsChartList');
        this.artistsChartGrid = document.getElementById('artistsChartGrid');
        this.albumsChartGrid = document.getElementById('albumsChartGrid');
        this.risingChartList = document.getElementById('risingChartList');

        // Statistics
        this.totalPlays = document.getElementById('totalPlays');
        this.totalTracks = document.getElementById('totalTracks');
        this.totalArtists = document.getElementById('totalArtists');
        this.totalCountries = document.getElementById('totalCountries');

        // Loading state
        this.loadingState = document.getElementById('loadingState');
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // Chart tabs
        this.chartTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const chartType = e.target.dataset.chart;
                this.setActiveChart(chartType);
            });
        });

        // Filters
        this.timeRangeFilter.addEventListener('change', (e) => {
            this.currentTimeRange = e.target.value;
            this.loadChartData();
        });

        this.genreFilter.addEventListener('change', (e) => {
            this.currentGenre = e.target.value;
            this.loadChartData();
        });

        // Hero actions
        this.heroPlayBtn.addEventListener('click', () => {
            this.playFeaturedTrack();
        });

        this.heroLikeBtn.addEventListener('click', () => {
            this.likeFeaturedTrack();
        });

        this.heroAddBtn.addEventListener('click', () => {
            this.addFeaturedToPlaylist();
        });

        // View all buttons
        document.querySelectorAll('.view-all-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.dataset.type;
                this.viewAllCharts(type);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.refreshCharts();
            }
        });
    }

    loadInitialData() {
        this.showLoadingState();

        // Simulate API call delay
        setTimeout(() => {
            this.loadChartData();
            this.hideLoadingState();
        }, 1500);
    }

    setActiveChart(chartType) {
        this.currentChart = chartType;

        // Update active tab
        this.chartTabs.forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

        this.loadChartData();
        this.announceChartChange(chartType);
    }

    loadChartData() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoadingState();

        // Simulate API call
        setTimeout(() => {
            const data = this.generateMockChartData();
            this.renderChartData(data);
            this.updateStatistics(data.statistics);
            this.hideLoadingState();
            this.isLoading = false;
        }, 800 + Math.random() * 700);
    }

    generateMockChartData() {
        const mockSongs = [
            { id: 1, title: 'Cosmic Dreams', artist: 'Stellar Waves', plays: '2.4M', change: 'up', changeValue: '+3', image: 'imgs/album-01.png' },
            { id: 2, title: 'Neon Nights', artist: 'Electric Pulse', plays: '2.1M', change: 'down', changeValue: '-1', image: 'imgs/album-02.png' },
            { id: 3, title: 'Digital Love', artist: 'Cyber Hearts', plays: '1.9M', change: 'up', changeValue: '+2', image: 'imgs/album-03.png' },
            { id: 4, title: 'Midnight Vibes', artist: 'Luna Echo', plays: '1.7M', change: 'new', changeValue: 'NEW', image: 'imgs/album-04.png' },
            { id: 5, title: 'Quantum Beat', artist: 'Atomic Sound', plays: '1.5M', change: 'up', changeValue: '+5', image: 'imgs/album-05.png' },
            { id: 6, title: 'Stellar Journey', artist: 'Cosmic Drift', plays: '1.3M', change: 'down', changeValue: '-2', image: 'imgs/album-01.png' },
            { id: 7, title: 'Electric Dreams', artist: 'Neon Pulse', plays: '1.2M', change: 'up', changeValue: '+1', image: 'imgs/album-02.png' },
            { id: 8, title: 'Future Waves', artist: 'Digital Storm', plays: '1.1M', change: 'up', changeValue: '+4', image: 'imgs/album-03.png' },
            { id: 9, title: 'Cyber Nights', artist: 'Tech Beats', plays: '1.0M', change: 'down', changeValue: '-3', image: 'imgs/album-04.png' },
            { id: 10, title: 'Galactic Sounds', artist: 'Space Echo', plays: '950K', change: 'new', changeValue: 'NEW', image: 'imgs/album-05.png' }
        ];

        const mockArtists = [
            { id: 1, name: 'Stellar Waves', genre: 'Electronic', followers: '2.1M', image: 'imgs/artist.png.png' },
            { id: 2, name: 'Electric Pulse', genre: 'Synthwave', followers: '1.8M', image: 'imgs/artist.png.png' },
            { id: 3, name: 'Cyber Hearts', genre: 'Future Pop', followers: '1.5M', image: 'imgs/artist.png.png' },
            { id: 4, name: 'Luna Echo', genre: 'Ambient', followers: '1.2M', image: 'imgs/artist.png.png' },
            { id: 5, name: 'Atomic Sound', genre: 'Techno', followers: '1.0M', image: 'imgs/artist.png.png' },
            { id: 6, name: 'Cosmic Drift', genre: 'Space Rock', followers: '850K', image: 'imgs/artist.png.png' }
        ];

        const mockAlbums = [
            { id: 1, title: 'Galaxy Sounds', artist: 'Stellar Waves', year: '2024', tracks: 12, image: 'imgs/album-01.png' },
            { id: 2, title: 'City Lights', artist: 'Electric Pulse', year: '2023', tracks: 10, image: 'imgs/album-02.png' },
            { id: 3, title: 'Future Romance', artist: 'Cyber Hearts', year: '2024', tracks: 8, image: 'imgs/album-03.png' },
            { id: 4, title: 'Nocturnal', artist: 'Luna Echo', year: '2024', tracks: 14, image: 'imgs/album-04.png' },
            { id: 5, title: 'Particle Dance', artist: 'Atomic Sound', year: '2023', tracks: 11, image: 'imgs/album-05.png' },
            { id: 6, title: 'Stellar Journey', artist: 'Cosmic Drift', year: '2024', tracks: 9, image: 'imgs/album-01.png' }
        ];

        const mockRising = [
            { id: 1, title: 'Rising Star', artist: 'New Wave', plays: '500K', change: 'up', changeValue: '+15', image: 'imgs/album-01.png' },
            { id: 2, title: 'Breakthrough', artist: 'Fresh Beat', plays: '450K', change: 'up', changeValue: '+12', image: 'imgs/album-02.png' },
            { id: 3, title: 'Viral Hit', artist: 'Trend Setter', plays: '400K', change: 'up', changeValue: '+20', image: 'imgs/album-03.png' },
            { id: 4, title: 'Next Level', artist: 'Future Star', plays: '350K', change: 'up', changeValue: '+18', image: 'imgs/album-04.png' },
            { id: 5, title: 'Momentum', artist: 'Rising Force', plays: '300K', change: 'up', changeValue: '+25', image: 'imgs/album-05.png' }
        ];

        return {
            featured: mockSongs[0],
            songs: mockSongs,
            artists: mockArtists,
            albums: mockAlbums,
            rising: mockRising,
            statistics: {
                totalPlays: '847M',
                totalTracks: '12.4K',
                totalArtists: '3.2K',
                totalCountries: '195'
            }
        };
    }

    renderChartData(data) {
        this.renderFeaturedHero(data.featured);
        this.renderSongsChart(data.songs);
        this.renderArtistsChart(data.artists);
        this.renderAlbumsChart(data.albums);
        this.renderRisingChart(data.rising);
    }

    renderFeaturedHero(featured) {
        this.heroBgImage.src = featured.image;
        this.heroTitle.textContent = featured.title;
        this.heroArtist.textContent = `by ${featured.artist}`;
        this.heroPlays.textContent = `${featured.plays} plays`;
        this.heroLikes.textContent = `${Math.floor(Math.random() * 200 + 50)}K likes`;
        this.heroShares.textContent = `${Math.floor(Math.random() * 50 + 10)}K shares`;
    }

    renderSongsChart(songs) {
        this.songsChartList.innerHTML = songs.slice(0, 10).map((song, index) => `
            <div class="chart-item" data-id="${song.id}">
                <div class="chart-position ${index < 3 ? 'top-3' : ''}">${index + 1}</div>
                <div class="chart-item-image">
                    <img src="${song.image}" alt="${song.title}" loading="lazy">
                </div>
                <div class="chart-item-info">
                    <div class="chart-item-title">${song.title}</div>
                    <div class="chart-item-artist">${song.artist}</div>
                </div>
                <div class="chart-item-stats">
                    <div class="chart-item-plays">${song.plays}</div>
                    <div class="chart-item-change ${song.change}">
                        ${song.change === 'up' ? '<i class="fas fa-arrow-up"></i>' :
                          song.change === 'down' ? '<i class="fas fa-arrow-down"></i>' :
                          '<i class="fas fa-star"></i>'}
                        ${song.changeValue}
                    </div>
                </div>
                <div class="chart-item-actions">
                    <button type="button" class="chart-action-btn play" aria-label="Play ${song.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Like ${song.title}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Add ${song.title} to playlist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderArtistsChart(artists) {
        this.artistsChartGrid.innerHTML = artists.slice(0, 6).map((artist, index) => `
            <div class="chart-card" data-id="${artist.id}">
                <div class="chart-card-position">${index + 1}</div>
                <div class="chart-card-image artist">
                    <img src="${artist.image}" alt="${artist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="chart-action-btn play" aria-label="Play ${artist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-card-title">${artist.name}</div>
                <div class="chart-card-subtitle">${artist.genre}</div>
                <div class="chart-card-stats">${artist.followers} followers</div>
            </div>
        `).join('');
    }

    renderAlbumsChart(albums) {
        this.albumsChartGrid.innerHTML = albums.slice(0, 6).map((album, index) => `
            <div class="chart-card" data-id="${album.id}">
                <div class="chart-card-position">${index + 1}</div>
                <div class="chart-card-image">
                    <img src="${album.image}" alt="${album.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="chart-action-btn play" aria-label="Play ${album.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-card-title">${album.title}</div>
                <div class="chart-card-subtitle">${album.artist}</div>
                <div class="chart-card-stats">${album.year} • ${album.tracks} tracks</div>
            </div>
        `).join('');
    }

    renderRisingChart(rising) {
        this.risingChartList.innerHTML = rising.slice(0, 5).map((track, index) => `
            <div class="chart-item" data-id="${track.id}">
                <div class="chart-position">${index + 1}</div>
                <div class="chart-item-image">
                    <img src="${track.image}" alt="${track.title}" loading="lazy">
                </div>
                <div class="chart-item-info">
                    <div class="chart-item-title">${track.title}</div>
                    <div class="chart-item-artist">${track.artist}</div>
                </div>
                <div class="chart-item-stats">
                    <div class="chart-item-plays">${track.plays}</div>
                    <div class="chart-item-change ${track.change}">
                        <i class="fas fa-rocket"></i>
                        ${track.changeValue}
                    </div>
                </div>
                <div class="chart-item-actions">
                    <button type="button" class="chart-action-btn play" aria-label="Play ${track.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Like ${track.title}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Add ${track.title} to playlist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateStatistics(stats) {
        this.totalPlays.textContent = stats.totalPlays;
        this.totalTracks.textContent = stats.totalTracks;
        this.totalArtists.textContent = stats.totalArtists;
        this.totalCountries.textContent = stats.totalCountries;
    }

    showLoadingState() {
        this.loadingState.classList.remove('hidden');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.opacity = '0.5';
        });
    }

    hideLoadingState() {
        this.loadingState.classList.add('hidden');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.opacity = '1';
        });
    }

    playFeaturedTrack() {
        // Simulate playing the featured track
        this.heroPlayBtn.innerHTML = '<i class="fas fa-pause"></i> Playing...';
        this.announceAction('Playing featured track');

        setTimeout(() => {
            this.heroPlayBtn.innerHTML = '<i class="fas fa-play"></i> Play Now';
        }, 3000);
    }

    likeFeaturedTrack() {
        const isLiked = this.heroLikeBtn.classList.contains('liked');

        if (isLiked) {
            this.heroLikeBtn.classList.remove('liked');
            this.heroLikeBtn.innerHTML = '<i class="fas fa-heart"></i> Like';
            this.announceAction('Removed from liked songs');
        } else {
            this.heroLikeBtn.classList.add('liked');
            this.heroLikeBtn.innerHTML = '<i class="fas fa-heart" style="color: var(--cosmic-pink);"></i> Liked';
            this.announceAction('Added to liked songs');
        }
    }

    addFeaturedToPlaylist() {
        // Simulate adding to playlist
        this.heroAddBtn.innerHTML = '<i class="fas fa-check"></i> Added';
        this.announceAction('Added to playlist');

        setTimeout(() => {
            this.heroAddBtn.innerHTML = '<i class="fas fa-plus"></i> Add to Playlist';
        }, 2000);
    }

    viewAllCharts(type) {
        // Simulate navigation to full charts page
        const chartNames = {
            songs: 'Top 100 Songs',
            artists: 'Top 50 Artists',
            albums: 'Top 50 Albums',
            rising: 'Rising Stars'
        };

        this.announceAction(`Viewing ${chartNames[type]} chart`);
        // In a real app, this would navigate to a dedicated page
        console.log(`Navigating to ${chartNames[type]} page`);
    }

    refreshCharts() {
        this.announceAction('Refreshing charts...');
        this.loadChartData();
    }

    announceChartChange(chartType) {
        const chartNames = {
            global: 'Global Charts',
            country: 'Country Charts',
            viral: 'Viral Charts',
            new: 'New Releases'
        };

        this.announceAction(`Switched to ${chartNames[chartType]}`);
    }

    announceAction(message) {
        this.liveRegion.textContent = message;

        // Clear the message after a short delay
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
    }
}

// Initialize the trending charts manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chartsManager = new TrendingChartsManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrendingChartsManager;
}