<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/player.css">
    <title>Now Playing - BansheeBlast</title>
</head>
<body>

    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html" aria-current="page">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container">
        <section class="player-container" aria-labelledby="player-heading">
            <h2 id="player-heading">Now Playing</h2>
            <div class="player-content">
                <audio id="audio" preload="metadata" src="demo/demo-track.mp3"></audio>
                <div class="album-art-container">
                    <img class="album-art" src="imgs/album-02.png" alt="Album Art" aria-describedby="song-title artist-name">
                </div>
                <div class="track-info">
                    <h3 id="song-title">Demo Song</h3>
                    <p id="artist-name">Demo Artist</p>
                </div>
                <div class="progress-container" aria-label="Track progress">
                    <div class="progress-bar">
                        <div class="progress" id="progress"></div>
                    </div>
                    <div class="time">
                        <span id="current-time">0:00</span>
                        <span id="total-time">0:00</span>
                    </div>
                </div>
                <div class="visualizer-container" aria-hidden="true">
                    <canvas id="audioVisualizer"></canvas>
                </div>
                <div class="player-controls">
                    <div class="primary-controls">
                        <button type="button" id="shuffle-btn" class="control-button" aria-label="Shuffle" data-tooltip="Shuffle">
                            <i class="fas fa-random"></i>
                        </button>
                        <button type="button" id="prev" class="control-button" aria-label="Previous track" data-tooltip="Previous">
                            <i class="fas fa-backward"></i>
                        </button>
                        <button type="button" id="play-pause" class="control-button" aria-label="Play or pause track" data-tooltip="Play/Pause">
                            <i class="fas fa-play play-icon"></i>
                            <i class="fas fa-pause pause-icon hidden"></i>
                        </button>
                        <button type="button" id="next" class="control-button" aria-label="Next track" data-tooltip="Next">
                            <i class="fas fa-forward"></i>
                        </button>
                        <button type="button" id="repeat-btn" class="control-button" aria-label="Repeat" data-tooltip="Repeat">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                    <div class="volume-control volume-control-centered">
                        <i class="fas fa-volume-up"></i>
                        <input type="range"
                               id="volume"
                               min="0"
                               max="1"
                               step="0.01"
                               value="1"
                               aria-label="Volume control">
                    </div>
                    <div class="additional-controls">
                        <button type="button" id="hd-btn" class="hd-button" aria-label="Toggle HD quality" data-tooltip="HD Audio">
                            HD
                        </button>
                        <button type="button" id="lyrics-btn" class="control-button" aria-label="Show lyrics" data-tooltip="Lyrics">
                            <i class="fas fa-microphone-alt"></i>
                        </button>
                        <button type="button" id="queue-btn" class="control-button" aria-label="Show queue" data-tooltip="Queue">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                <div class="playlist-section">
                    <div class="playlist-header">
                        <h3>Current Playlist</h3>
                        <span id="playlist-name">My Favorites</span>
                    </div>
                    <div class="playlist-container">
                        <ul id="playlist" class="playlist">
                            <li class="playlist-item active playing" tabindex="0">
                                <div class="playlist-item-info">
                                    <span class="playlist-item-title">Demo Song</span>
                                    <span class="playlist-item-artist">Demo Artist</span>
                                </div>
                                <span class="playlist-item-duration">3:45</span>
                                <span class="playing-indicator" aria-label="Now Playing"></span>
                            </li>
                            <li class="playlist-item" tabindex="0">
                                <div class="playlist-item-info">
                                    <span class="playlist-item-title">Another Song</span>
                                    <span class="playlist-item-artist">Another Artist</span>
                                </div>
                                <span class="playlist-item-duration">4:12</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lyrics-panel" id="lyrics-panel" hidden>
                    <div class="lyrics-header">
                        <h3>Lyrics</h3>
                        <button type="button" class="close-lyrics" id="close-lyrics" aria-label="Close lyrics panel">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="lyrics-content" id="lyrics-content">
                        <!-- Lyrics will be displayed here -->
                    </div>
                </div>
            </div>
        </section>
    </main>
    <script src="js/player.js"></script>
</body>
</html>