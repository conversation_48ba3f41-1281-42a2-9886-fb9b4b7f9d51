document.addEventListener("DOMContentLoaded", function () {
    const particles = document.querySelectorAll('.particles-container .particle');
    particles.forEach((particle) => {
        // Random position within the container
        particle.style.top = `${Math.random() * 90 + 2}%`;
        particle.style.left = `${Math.random() * 90 + 2}%`;

        // Random animation delay and duration for variety
        particle.style.animationDelay = `${Math.random() * 10}s`;
        const duration = 8 + Math.random() * 8;
        particle.style.animationDuration = `${duration}s`;
        particle.style.animationName = 'particleDrift';
        particle.style.animationTimingFunction = 'linear';
        particle.style.animationIterationCount = 'infinite';

        // Random drift direction and distance
        const angle = Math.random() * 2 * Math.PI;
        const distance = 30 + Math.random() * 40; // px
        const dx = Math.cos(angle) * distance;
        const dy = Math.sin(angle) * distance;
        particle.style.setProperty('--dx', `${dx}px`);
        particle.style.setProperty('--dy', `${dy}px`);
    });
});