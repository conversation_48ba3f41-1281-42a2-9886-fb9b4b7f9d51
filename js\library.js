// Enhanced Library Page Management System
class LibraryManager {
    constructor() {
        this.searchTimeout = null;
        this.currentFilter = 'all';
        this.sortOrder = 'recent';
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.initializeParticles();
        this.animateStats();
        this.setupIntersectionObserver();
    }

    bindElements() {
        // Search elements
        this.searchInput = document.getElementById('librarySearch');
        this.searchClear = document.getElementById('libraryClear');

        // Navigation tabs
        this.navTabs = document.querySelectorAll('.nav-tab');

        // Action buttons
        this.shuffleAllBtn = document.getElementById('shuffleAllBtn');
        this.importMusicBtn = document.getElementById('importMusicBtn');
        this.createPlaylistBtn = document.querySelector('.create-playlist-btn');
        this.playAllBtn = document.querySelector('.play-all-btn');

        // Statistics elements
        this.statNumbers = document.querySelectorAll('.stat-number');

        // Cards
        this.libraryCards = document.querySelectorAll('.library-card, .playlist-card, .liked-song-card, .artist-card');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region') || this.createLiveRegion();
    }

    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Navigation tabs
        this.navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.switchSection(section);
            });
        });

        // Action buttons
        if (this.shuffleAllBtn) {
            this.shuffleAllBtn.addEventListener('click', () => {
                this.shuffleAll();
            });
        }

        if (this.importMusicBtn) {
            this.importMusicBtn.addEventListener('click', () => {
                this.importMusic();
            });
        }

        if (this.createPlaylistBtn) {
            this.createPlaylistBtn.addEventListener('click', () => {
                this.createPlaylist();
            });
        }

        if (this.playAllBtn) {
            this.playAllBtn.addEventListener('click', () => {
                this.playAllLikedSongs();
            });
        }

        // Card interactions
        this.libraryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleCardClick(e, card);
            });

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(e, card);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput?.focus();
            }
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.createPlaylist();
            }
        });
    }

    initializeParticles() {
        const particles = document.querySelectorAll('.particles-container .particle');
        particles.forEach((particle, index) => {
            // Random position within the container
            particle.style.top = `${Math.random() * 90 + 2}%`;
            particle.style.left = `${Math.random() * 90 + 2}%`;

            // Random animation delay and duration for variety
            particle.style.animationDelay = `${Math.random() * 10}s`;
            const duration = 8 + Math.random() * 8;
            particle.style.animationDuration = `${duration}s`;
            particle.style.animationName = 'particleDrift';
            particle.style.animationTimingFunction = 'linear';
            particle.style.animationIterationCount = 'infinite';

            // Random drift direction and distance
            const angle = Math.random() * 2 * Math.PI;
            const distance = 30 + Math.random() * 40; // px
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;
            particle.style.setProperty('--dx', `${dx}px`);
            particle.style.setProperty('--dy', `${dy}px`);

            // Add different colors for variety
            const colors = ['var(--neon-blue)', 'var(--cosmic-pink)', 'var(--electric-violet)', 'var(--cyber-lime)'];
            const color = colors[index % colors.length];
            particle.style.background = `radial-gradient(circle, ${color}, transparent)`;
        });
    }

    animateStats() {
        this.statNumbers.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');

            if (!isNaN(numericValue)) {
                // Animate from 0 to final value
                let currentValue = 0;
                const increment = numericValue / 60;
                const duration = 2000; // 2 seconds
                const stepTime = duration / 60;

                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }

                        if (suffix.includes('h')) {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        } else {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        }
                    }, stepTime);
                }, index * 300); // Stagger animations
            }
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        this.libraryCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    handleSearch(query) {
        // Show/hide clear button
        if (query.trim()) {
            this.searchClear?.classList.remove('hidden');
        } else {
            this.searchClear?.classList.add('hidden');
        }

        // Debounce search
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query.trim());
        }, 300);
    }

    performSearch(query) {
        if (!query) {
            this.showAllCards();
            return;
        }

        this.libraryCards.forEach(card => {
            const title = card.querySelector('h3, .playlist-title, .song-title')?.textContent.toLowerCase() || '';
            const artist = card.querySelector('.artist-name, .playlist-artist')?.textContent.toLowerCase() || '';

            if (title.includes(query.toLowerCase()) || artist.includes(query.toLowerCase())) {
                card.style.display = 'block';
                card.classList.add('search-match');
            } else {
                card.style.display = 'none';
                card.classList.remove('search-match');
            }
        });

        this.announceAction(`Found ${document.querySelectorAll('.search-match').length} results for "${query}"`);
    }

    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.searchClear?.classList.add('hidden');
            this.showAllCards();
            this.searchInput.focus();
            this.announceAction('Search cleared');
        }
    }

    showAllCards() {
        this.libraryCards.forEach(card => {
            card.style.display = 'block';
            card.classList.remove('search-match');
        });
    }

    switchSection(section) {
        // Update active tab
        this.navTabs.forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

        // Show/hide sections (if implemented)
        this.announceAction(`Switched to ${section} section`);
    }

    shuffleAll() {
        this.announceAction('Shuffling all music in your library');
        // Simulate shuffle functionality
        console.log('Shuffling all music...');
    }

    importMusic() {
        this.announceAction('Opening music import dialog');
        // Simulate file import
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        input.accept = 'audio/*';
        input.onchange = (e) => {
            const files = e.target.files;
            this.announceAction(`Selected ${files.length} file(s) for import`);
        };
        input.click();
    }

    createPlaylist() {
        this.announceAction('Navigating to create playlist page');
        window.location.href = 'createplaylist.html';
    }

    playAllLikedSongs() {
        this.announceAction('Playing all liked songs');
        // Simulate playing all liked songs
        console.log('Playing all liked songs...');
    }

    handleCardClick(_, card) {
        const cardType = card.classList.contains('playlist-card') ? 'playlist' :
                        card.classList.contains('liked-song-card') ? 'song' :
                        card.classList.contains('artist-card') ? 'artist' : 'library';

        const title = card.querySelector('h3, .playlist-title, .song-title')?.textContent || 'Unknown';

        this.announceAction(`Opening ${cardType}: ${title}`);

        // Simulate navigation based on card type
        switch(cardType) {
            case 'playlist':
                console.log(`Opening playlist: ${title}`);
                break;
            case 'song':
                console.log(`Playing song: ${title}`);
                break;
            case 'artist':
                console.log(`Opening artist page: ${title}`);
                break;
            default:
                console.log(`Opening ${title}`);
        }
    }

    createLiveRegion() {
        const liveRegion = document.createElement('div');
        liveRegion.id = 'aria-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        return liveRegion;
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;

            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }

    // Method to update library statistics
    updateStats(stats) {
        const elements = {
            totalPlaylists: document.getElementById('totalPlaylists'),
            totalLikedSongs: document.getElementById('totalLikedSongs'),
            totalListeningTime: document.getElementById('totalListeningTime'),
            totalArtists: document.getElementById('totalArtists')
        };

        Object.keys(stats).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = stats[key];
            }
        });
    }

    // Method to add shimmer loading effect
    addLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.add('loading-shimmer');
        });
    }

    removeLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.remove('loading-shimmer');
        });
    }
}

// Initialize the library manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.libraryManager = new LibraryManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LibraryManager;
}