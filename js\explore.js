// Explore Page Management System
class ExplorePageManager {
    constructor() {
        this.searchTimeout = null;
        this.isSearching = false;
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.initializeAnimations();
    }

    bindElements() {
        // Search elements
        this.searchInput = document.getElementById('searchInput');
        this.searchClear = document.getElementById('searchClear');
        this.searchForm = document.querySelector('.search-form');
        this.searchTags = document.querySelectorAll('.search-tag');

        // Genre cards
        this.genreCards = document.querySelectorAll('.genre-card');

        // Hero elements
        this.heroStats = document.querySelectorAll('.stat-number');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });

            this.searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch(this.searchInput.value);
                }
            });
        }

        // Search form submission
        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(this.searchInput.value);
            });
        }

        // Clear search button
        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Quick search tags
        this.searchTags.forEach(tag => {
            tag.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                this.searchInput.value = query;
                this.performSearch(query);
            });
        });

        // Genre cards
        this.genreCards.forEach(card => {
            card.addEventListener('click', () => {
                const genre = card.dataset.genre;
                this.exploreGenre(genre);
            });

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const genre = card.dataset.genre;
                    this.exploreGenre(genre);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput.focus();
            }
        });
    }

    initializeAnimations() {
        // Animate hero stats on page load
        this.animateHeroStats();

        // Add intersection observer for genre cards
        this.observeGenreCards();
    }

    handleSearchInput(value) {
        // Show/hide clear button
        if (value.trim()) {
            this.searchClear.classList.remove('hidden');
        } else {
            this.searchClear.classList.add('hidden');
        }

        // Debounce search suggestions
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            if (value.trim().length >= 2) {
                this.showSearchSuggestions(value.trim());
            }
        }, 300);
    }

    performSearch(query) {
        if (!query.trim()) {
            this.announceAction('Please enter a search term');
            return;
        }

        this.isSearching = true;
        this.announceAction(`Searching for "${query}"`);

        // Simulate search by redirecting to search results page
        const searchUrl = `searchresults.html?q=${encodeURIComponent(query)}`;
        window.location.href = searchUrl;
    }

    clearSearch() {
        this.searchInput.value = '';
        this.searchClear.classList.add('hidden');
        this.searchInput.focus();
        this.announceAction('Search cleared');
    }

    showSearchSuggestions(query) {
        // In a real app, this would show dropdown suggestions
        console.log(`Showing suggestions for: ${query}`);
    }

    exploreGenre(genre) {
        this.announceAction(`Exploring ${genre} music`);

        // Simulate navigation to genre page
        const genreUrl = `searchresults.html?genre=${encodeURIComponent(genre)}`;
        window.location.href = genreUrl;
    }

    animateHeroStats() {
        this.heroStats.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');

            // Animate from 0 to final value
            let currentValue = 0;
            const increment = numericValue / 50;
            const duration = 2000; // 2 seconds
            const stepTime = duration / 50;

            setTimeout(() => {
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= numericValue) {
                        currentValue = numericValue;
                        clearInterval(timer);
                    }

                    stat.textContent = currentValue.toFixed(1) + suffix;
                }, stepTime);
            }, index * 200); // Stagger animations
        });
    }

    observeGenreCards() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        this.genreCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;

            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }
}

// Enhanced Carousel Management (if not handled by main.js)
class EnhancedCarousel {
    constructor(container) {
        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.cards = container.querySelectorAll('.carousel-card');
        this.prevBtn = container.querySelector('.carousel-button.prev');
        this.nextBtn = container.querySelector('.carousel-button.next');
        this.currentIndex = 0;
        this.cardWidth = 280;
        this.gap = 30;
        this.visibleCards = this.calculateVisibleCards();

        this.init();
    }

    init() {
        this.bindEvents();
        this.updateButtons();
        this.setupAutoPlay();
    }

    calculateVisibleCards() {
        const containerWidth = this.container.offsetWidth;
        return Math.floor((containerWidth - 120) / (this.cardWidth + this.gap));
    }

    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prev());
        }

        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.next());
        }

        // Touch/swipe support
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });

        this.track.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
            const diff = startX - currentX;

            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    this.next();
                } else {
                    this.prev();
                }
                isDragging = false;
            }
        });

        this.track.addEventListener('touchend', () => {
            isDragging = false;
        });

        // Keyboard navigation
        this.container.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                this.prev();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                this.next();
            }
        });

        // Resize handler
        window.addEventListener('resize', () => {
            this.visibleCards = this.calculateVisibleCards();
            this.updateButtons();
        });
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updateCarousel();
        }
    }

    next() {
        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);
        if (this.currentIndex < maxIndex) {
            this.currentIndex++;
            this.updateCarousel();
        }
    }

    updateCarousel() {
        const translateX = -(this.currentIndex * (this.cardWidth + this.gap));
        this.track.style.transform = `translateX(${translateX}px)`;
        this.updateButtons();
    }

    updateButtons() {
        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);

        if (this.prevBtn) {
            this.prevBtn.disabled = this.currentIndex === 0;
            this.prevBtn.style.opacity = this.currentIndex === 0 ? '0.5' : '1';
        }

        if (this.nextBtn) {
            this.nextBtn.disabled = this.currentIndex >= maxIndex;
            this.nextBtn.style.opacity = this.currentIndex >= maxIndex ? '0.5' : '1';
        }
    }

    setupAutoPlay() {
        // Optional auto-play functionality
        setInterval(() => {
            const maxIndex = Math.max(0, this.cards.length - this.visibleCards);
            if (this.currentIndex >= maxIndex) {
                this.currentIndex = 0;
            } else {
                this.currentIndex++;
            }
            this.updateCarousel();
        }, 5000); // Auto-advance every 5 seconds
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize explore page manager
    window.exploreManager = new ExplorePageManager();

    // Initialize enhanced carousels
    const carousels = document.querySelectorAll('.carousel-container');
    carousels.forEach(carousel => {
        new EnhancedCarousel(carousel);
    });
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ExplorePageManager, EnhancedCarousel };
}