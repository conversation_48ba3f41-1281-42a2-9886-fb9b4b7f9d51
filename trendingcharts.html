<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Trending Charts - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/trendingcharts.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container">
        <div class="trending-charts-page">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="charts-title-gradient">Trending Charts</h1>
                <p class="page-description">Discover what's hot right now - the most popular songs, albums, and artists</p>
            </div>

            <!-- Chart Navigation -->
            <div class="chart-navigation">
                <div class="chart-tabs">
                    <button type="button" class="chart-tab active" data-chart="global">
                        <i class="fas fa-globe"></i>
                        Global
                    </button>
                    <button type="button" class="chart-tab" data-chart="country">
                        <i class="fas fa-flag"></i>
                        Your Country
                    </button>
                    <button type="button" class="chart-tab" data-chart="viral">
                        <i class="fas fa-fire"></i>
                        Viral
                    </button>
                    <button type="button" class="chart-tab" data-chart="new">
                        <i class="fas fa-star"></i>
                        New Releases
                    </button>
                </div>

                <div class="chart-filters">
                    <select id="timeRangeFilter" class="chart-filter" aria-label="Select time range for charts">
                        <option value="daily">Today</option>
                        <option value="weekly" selected>This Week</option>
                        <option value="monthly">This Month</option>
                        <option value="yearly">This Year</option>
                    </select>

                    <select id="genreFilter" class="chart-filter" aria-label="Filter charts by genre">
                        <option value="all">All Genres</option>
                        <option value="pop">Pop</option>
                        <option value="rock">Rock</option>
                        <option value="hip-hop">Hip Hop</option>
                        <option value="electronic">Electronic</option>
                        <option value="r&b">R&B</option>
                        <option value="country">Country</option>
                        <option value="jazz">Jazz</option>
                        <option value="classical">Classical</option>
                    </select>
                </div>
            </div>

            <!-- Chart Content -->
            <div class="chart-content">
                <!-- Featured Chart Hero -->
                <section class="featured-chart-hero" id="featuredChartHero">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                        <img src="imgs/album-01.png" alt="Featured track" class="hero-bg-image" id="heroBgImage">
                    </div>
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-crown"></i>
                            #1 Trending
                        </div>
                        <h2 class="hero-title" id="heroTitle">Cosmic Dreams</h2>
                        <p class="hero-artist" id="heroArtist">by Stellar Waves</p>
                        <div class="hero-stats">
                            <span class="stat">
                                <i class="fas fa-play"></i>
                                <span id="heroPlays">2.4M plays</span>
                            </span>
                            <span class="stat">
                                <i class="fas fa-heart"></i>
                                <span id="heroLikes">156K likes</span>
                            </span>
                            <span class="stat">
                                <i class="fas fa-share"></i>
                                <span id="heroShares">23K shares</span>
                            </span>
                        </div>
                        <div class="hero-actions">
                            <button type="button" class="hero-play-btn" id="heroPlayBtn">
                                <i class="fas fa-play"></i>
                                Play Now
                            </button>
                            <button type="button" class="hero-action-btn" id="heroLikeBtn">
                                <i class="fas fa-heart"></i>
                                Like
                            </button>
                            <button type="button" class="hero-action-btn" id="heroAddBtn">
                                <i class="fas fa-plus"></i>
                                Add to Playlist
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Charts Grid -->
                <div class="charts-grid">
                    <!-- Top Songs Chart -->
                    <section class="chart-section songs-chart" id="songsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-music"></i>
                                Top Songs
                            </h2>
                            <button type="button" class="view-all-btn" data-type="songs">
                                View All 100
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-list" id="songsChartList">
                            <!-- Songs will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Top Artists Chart -->
                    <section class="chart-section artists-chart" id="artistsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-microphone"></i>
                                Top Artists
                            </h2>
                            <button type="button" class="view-all-btn" data-type="artists">
                                View All 50
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-grid artists-grid" id="artistsChartGrid">
                            <!-- Artists will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Top Albums Chart -->
                    <section class="chart-section albums-chart" id="albumsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-compact-disc"></i>
                                Top Albums
                            </h2>
                            <button type="button" class="view-all-btn" data-type="albums">
                                View All 50
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-grid albums-grid" id="albumsChartGrid">
                            <!-- Albums will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Rising Stars -->
                    <section class="chart-section rising-chart" id="risingChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-rocket"></i>
                                Rising Stars
                            </h2>
                            <button type="button" class="view-all-btn" data-type="rising">
                                View All
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-list rising-list" id="risingChartList">
                            <!-- Rising tracks will be dynamically inserted here -->
                        </div>
                    </section>
                </div>

                <!-- Chart Statistics -->
                <section class="chart-stats-section">
                    <h2>Chart Statistics</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalPlays">847M</div>
                                <div class="stat-label">Total Plays This Week</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-music"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalTracks">12.4K</div>
                                <div class="stat-label">Tracks in Charts</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalArtists">3.2K</div>
                                <div class="stat-label">Featured Artists</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalCountries">195</div>
                                <div class="stat-label">Countries Tracked</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Loading State -->
            <div class="loading-state hidden" id="loadingState">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p>Loading trending charts...</p>
            </div>
        </div>

        <div id="aria-live-region" aria-live="polite" class="sr-only"></div>
    </main>

    <script src="js/trendingcharts.js"></script>
</body>
</html>