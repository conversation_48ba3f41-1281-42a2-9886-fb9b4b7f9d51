<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/notification.css">
   
    <title>Document</title>
</head>


<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html" aria-current="page">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>


    
    <main id="main-content" class="container">
        <h1 class="notification-title-gradient">Notifications</h1>
        <div class="notifications-header">
            <button id="markAllReadBtn" class="mark-all-btn">Mark All as Read</button>
            <button id="clearAllBtn" class="clear-all-btn">Clear All</button>
        </div>
        <div class="notifications-container">
            <!-- Notifications will be dynamically inserted here -->
        </div>
        <div id="aria-live-region" aria-live="polite" style="position:absolute;left:-9999px;top:auto;width:1px;height:1px;overflow:hidden;"></div>
    </main>
    <script src="js/notification.js"></script>
</body>
</html>