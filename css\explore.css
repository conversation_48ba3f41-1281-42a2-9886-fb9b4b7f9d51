:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}

/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* ===== Hero Section ===== */
.explore-hero {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    padding: 4rem 2rem 3rem 2rem;
    margin-bottom: 2.5rem;
    margin-top: 110px; /* Increased from 90px to 110px for more space below navbar */
    border-radius: var(--border-radius-lg, 18px);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 320px;
    position: relative;
    /* Add animated gradient overlay */
}

.explore-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 70%
    ), linear-gradient(
        45deg,
        rgba(var(--neon-blue-rgb), 0.2) 0%,
        transparent 25%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 75%,
        rgba(255, 0, 110, 0.2) 100%
    );
    opacity: 0.8;
    mix-blend-mode: overlay;
    animation: shimmer 12s infinite ease-in-out;
    background-size: 200% 200%;
    border-radius: inherit;
    z-index: 1;
}

/* Add backdrop gradient */
.explore-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    /* Combine both gradients in a single background property */
    background:
        linear-gradient(
            135deg,
            rgba(var(--neon-blue-rgb), 0.05) 0%,
            rgba(var(--cosmic-pink-rgb), 0.05) 50%,
            rgba(var(--electric-violet-rgb), 0.05) 100%
        ),
        linear-gradient(
            120deg,
            rgba(111,0,255,0.10) 0%,
            rgba(0,224,255,0.08) 40%,
            rgba(255,0,110,0.10) 80%,
            rgba(111,0,255,0.10) 100%
        );
    background-size: 100% 100%, 200% 200%;
    opacity: 0.7;
    z-index: 0;
    animation: heroGradientMove 12s ease-in-out infinite;
    pointer-events: none;
    border-radius: inherit;
}

.explore-hero .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.explore-hero .hero-content h1 {
    font-size: 3em;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

.explore-hero .hero-subtitle {
    font-size: 1.4em;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
}

/* Add a call-to-action button for the hero section */
.explore-hero .cta-button {
    background: var(--gradient-primary);
    color: var(--text-primary);
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 1.2em;
    font-weight: 600;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-button);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-top: 1.5em;
}

.explore-hero .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left 0.6s ease;
}

.explore-hero .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-button-hover);
    text-shadow:
        0 0 8px rgba(255, 255, 255, 0.6),
        0 0 12px rgba(var(--neon-blue-rgb), 0.4);
}

.explore-hero .cta-button:hover::before {
    left: 100%;
}

.explore-hero .cta-button:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-button);
}

/* Responsive adjustments for hero section */
@media (max-width: 768px) {
    .explore-hero .hero-content h1 {
        font-size: 2em;
    }
    .explore-hero .cta-button {
        font-size: 1em;
        padding: 12px 28px;
    }
}

@media (max-width: 480px) {
    .explore-hero .hero-content h1 {
        font-size: 1.4em;
    }
    .explore-hero .cta-button {
        font-size: 0.95em;
        padding: 10px 18px;
    }
}

.results-container {
    margin-top: 0.5rem;
    min-height: 120px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.1rem;
    text-align: center;
    opacity: 0.7;
}

/* Search Section Styles */
.search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2.5rem 2rem 2rem 2rem;
    max-width: 900px;
    margin: 0 auto 2.5rem auto;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.search-input-wrapper {
    position: relative;
    flex: 1 1 320px;
    display: flex;
    align-items: center;
}

.search-bar {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 2.5rem;
    border-radius: 24px;
    border: 1px solid rgba(255,255,255,0.12);
    background: rgba(255,255,255,0.04);
    color: var(--text-primary);
    font-size: 1.1rem;
    outline: none;
    transition: border 0.2s;
}

.search-bar:focus {
    border: 1.5px solid var(--cosmic-pink);
    background: rgba(255,255,255,0.07);
}

.search-icon {
    position: absolute;
    left: 14px;
    color: var(--neon-blue);
    font-size: 1.1rem;
    pointer-events: none;
}

.search-clear {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
}

.search-clear:hover {
    color: var(--cosmic-pink);
}

.btn-medium.search-button {
    padding: 0.75rem 2rem;
    border-radius: 24px;
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s;
}

.btn-medium.search-button:hover,
.btn-medium.search-button:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
}

.filters-container {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1.2rem;
}

.filter-select {
    padding: 0.6rem 1.5rem 0.6rem 1rem;
    border-radius: 18px;
    border: 1px solid rgba(255,255,255,0.12);
    background: rgba(19, 21, 26, 0.95); /* Dark background for readability */
    color: var(--text-primary); /* Ensure text is white */
    font-size: 1rem;
    outline: none;
    transition: border 0.2s;
}

.filter-select:focus {
    border: 1.5px solid var(--neon-blue);
    background: rgba(26, 29, 36, 0.98); /* Slightly lighter on focus */
}

.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 120px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.1rem;
    text-align: center;
    opacity: 0.7;
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes shimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes heroGradientMove {
    0% {
        background-position: 0% 50%, 0% 50%;
    }
    50% {
        background-position: 0% 50%, 100% 50%;
    }
    100% {
        background-position: 0% 50%, 0% 50%;
    }
}

/* Featured Artists Section Styles */
.featured-artists {
    padding: 4rem 0;
    background: linear-gradient(180deg, rgba(19, 21, 26, 0.8) 0%, rgba(26, 29, 36, 0.8) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.featured-artists .section-header h2 {
    font-size: 2.2em;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

.featured-artists .section-description {
    color: rgba(255, 255, 255, 0.7);
    max-width: 800px;
    margin: 0 auto 24px;
    text-align: center;
    font-size: 1.1em;
    line-height: 1.6;
}

.featured-artists .carousel-container {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    overflow: hidden;
    padding: 1rem 60px;
    opacity: 1;
}

.featured-artists .carousel-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.3, 0, 0.3, 1);
    gap: 30px;
    padding: 1rem 0;
    will-change: transform;
    margin-left: auto;
    margin-right: auto;
    min-height: 340px; /* Prevents layout shift on load */
    overflow-x: auto; /* Enable horizontal scroll on small screens */
    white-space: nowrap; /* Prevent wrapping */
    scrollbar-width: none; /* Hide scrollbar in Firefox */
}

.featured-artists .carousel-track::-webkit-scrollbar {
    display: none; /* Hide scrollbar in Webkit browsers */
}

.featured-artists .carousel-card {
    flex: 0 0 280px;
    width: 280px;
    max-width: 280px;
    flex-shrink: 0; /* Prevent shrinking for horizontal scroll */
    min-width: 220px; /* Ensures cards are visible on mobile */
    transition: transform 0.5s cubic-bezier(0.3, 0, 0.3, 1), opacity 0.3s ease;
}

.featured-artists .card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    margin: 0;
    position: relative;
    overflow: hidden;
}

.featured-artists .card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.featured-artists .card::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
    z-index: 1;
}

.featured-artists .card:hover::before {
    transform: scaleX(1);
}

.featured-artists .img-container {
    width: 100%;
    height: 160px;
    position: relative;
    overflow: hidden;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
}

.featured-artists .img-container img {
    width: 85%;
    height: 85%;
    display: block;
    transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    object-fit: contain;
    object-position: center;
    filter: brightness(0.9);
}

.featured-artists .card:hover .img-container img {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.1);
}

.featured-artists .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.featured-artists .card:focus-within .play-overlay,
.featured-artists .card:hover .play-overlay {
    opacity: 1;
}

.featured-artists .play-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.featured-artists .play-button i {
    font-size: 1rem;
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.featured-artists .play-button:hover {
    transform: scale(1.1);
    box-shadow: 
        0 0 30px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(var(--neon-blue-rgb), 0.4);
}

.featured-artists .play-button:hover i {
    text-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(var(--neon-blue-rgb), 0.4);
}

.featured-artists .play-button:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
    transform: scale(1.1);
    box-shadow:
        0 0 30px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(var(--neon-blue-rgb), 0.6);
}

.featured-artists .play-button:focus i {
    text-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(var(--neon-blue-rgb), 0.6);
}

.featured-artists .card-content {
    padding: 12px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    text-align: center;
}

.featured-artists .text-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.featured-artists .text-content h3 {
    font-size: 1rem;
    margin: 0 0 4px 0;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
}

.featured-artists .text-content p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
    max-width: 90%;
}

.featured-artists .button {
    margin-top: auto;
    display: inline-block;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    padding: 8px 16px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.featured-artists .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    text-shadow:
        0 0 10px rgba(var(--neon-blue-rgb), 0.8),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.4);
}

.featured-artists .button:focus {
    outline: 2px solid var(--cosmic-pink);
    outline-offset: 2px;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    text-shadow:
        0 0 10px rgba(var(--neon-blue-rgb), 0.8),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.6);
}

/* Responsive adjustments for Featured Artists */
@media (max-width: 1024px) {
    .featured-artists .carousel-container {
        padding: 1rem 40px;
    }
    .featured-artists .carousel-card {
        min-width: 240px;
    }
}

@media (max-width: 768px) {
    .featured-artists {
        padding: 2rem 0;
    }
    .featured-artists .carousel-container {
        padding: 1rem 20px;
    }
    .featured-artists .carousel-card {
        min-width: 180px;
        width: 180px;
        max-width: 180px;
    }
    .featured-artists .section-header h2 {
        font-size: 2em;
    }
}

@media (max-width: 480px) {
    .featured-artists .carousel-card {
        min-width: 140px;
        width: 140px;
        max-width: 140px;
    }
    .featured-artists .section-header h2 {
        font-size: 1.4em;
    }
}

/* Enhanced Hero Section Styles */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    color: white;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.cta-button.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Enhanced Search Section Styles */
.discover-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 2.5rem;
    margin-bottom: 3rem;
}

.section-title {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.section-title i {
    color: var(--neon-blue);
    font-size: 1.5rem;
}

.section-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

.search-form {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.search-button {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.quick-search-tags {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.tags-label {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
}

.search-tag {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    font-size: 0.9rem;
}

.search-tag:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--neon-blue);
    transform: translateY(-1px);
}

.hidden {
    display: none !important;
}

/* Genre Section Styles */
.genre-section {
    margin-bottom: 4rem;
}

.genre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.genre-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 2rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.genre-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.genre-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    z-index: 1;
}

.genre-pattern {
    width: 100%;
    height: 100%;
    background-size: 50px 50px;
    animation: patternMove 20s linear infinite;
}

.pop-pattern {
    background: radial-gradient(circle, var(--cosmic-pink) 2px, transparent 2px);
}

.rock-pattern {
    background: linear-gradient(45deg, var(--neon-blue) 25%, transparent 25%);
}

.electronic-pattern {
    background: conic-gradient(var(--electric-violet), var(--neon-blue), var(--cosmic-pink), var(--electric-violet));
}

.hiphop-pattern {
    background: repeating-linear-gradient(90deg, var(--cosmic-pink), var(--cosmic-pink) 10px, transparent 10px, transparent 20px);
}

.jazz-pattern {
    background: radial-gradient(ellipse, var(--cyber-lime) 1px, transparent 1px);
}

.indie-pattern {
    background: linear-gradient(60deg, var(--neon-blue) 25%, transparent 25%);
}

.country-pattern {
    background: repeating-conic-gradient(var(--cosmic-pink) 0deg 30deg, transparent 30deg 60deg);
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-10px); }
    50% { transform: translateX(-20px) translateY(0); }
    75% { transform: translateX(-10px) translateY(10px); }
    100% { transform: translateX(0) translateY(0); }
}

.genre-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.genre-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3);
}

.genre-card h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.genre-card p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.genre-stats {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.genre-stats i {
    color: var(--neon-blue);
    margin-right: 0.25rem;
}

/* Utility classes */
.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Responsive Design for New Elements */
@media (max-width: 768px) {
    .hero-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .discover-section {
        padding: 2rem 1.5rem;
    }

    .search-form {
        flex-direction: column;
        gap: 1rem;
    }

    .search-button {
        width: 100%;
        justify-content: center;
    }

    .quick-search-tags {
        justify-content: center;
    }

    .genre-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .genre-card {
        padding: 1.5rem;
        min-height: 160px;
    }

    .genre-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .discover-section {
        padding: 1.5rem 1rem;
    }

    .section-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .quick-search-tags {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .search-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .genre-card {
        padding: 1.25rem;
        min-height: 140px;
    }

    .genre-card h3 {
        font-size: 1.3rem;
    }

    .genre-card p {
        font-size: 0.9rem;
    }
}